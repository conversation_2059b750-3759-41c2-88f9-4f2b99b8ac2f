/* Tutorial System Styles */

/* Tutorial Welcome Modal */
.tutorial-welcome-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tutorial-welcome-modal.show {
  opacity: 1;
  visibility: visible;
}

.tutorial-welcome-content {
  background: var(--container-bg);
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 60px var(--shadow-color);
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
  text-align: center;
  border: 1px solid var(--border-color);
}

.tutorial-welcome-modal.show .tutorial-welcome-content {
  transform: scale(1) translateY(0);
}

.tutorial-welcome-header {
  margin-bottom: 1.5rem;
}

.tutorial-welcome-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--accent-color) 0%,
    var(--header-bg) 100%
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: var(--header-text);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.tutorial-welcome-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem;
}

.tutorial-welcome-subtitle {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 1rem;
  margin: 0;
}

.tutorial-language-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin: 1rem 0 1.5rem;
  padding: 1rem;
  background: var(--card-bg);
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

.tutorial-language-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--text-color);
  font-weight: 600;
}

.tutorial-language-label i {
  color: var(--accent-color);
  font-size: 1rem;
}

.tutorial-language-select {
  padding: 0.5rem 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--container-bg);
  color: var(--text-color);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.tutorial-language-select:hover {
  border-color: var(--accent-color);
}

.tutorial-language-select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(165, 120, 101, 0.2);
}

.tutorial-welcome-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1.5rem 0;
}

.tutorial-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--card-bg);
  border-radius: 10px;
  font-size: 0.9rem;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.tutorial-feature i {
  color: var(--accent-color);
  font-size: 1.1rem;
}

.tutorial-welcome-description {
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 1.5rem 0;
}

.tutorial-welcome-footer {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Tutorial Buttons */
.tutorial-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  text-decoration: none;
}

.tutorial-btn-primary {
  background: linear-gradient(
    135deg,
    var(--accent-color) 0%,
    var(--header-bg) 100%
  );
  color: var(--header-text);
}

.tutorial-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(165, 120, 101, 0.4);
  background: linear-gradient(
    135deg,
    var(--accent-color-hover) 0%,
    var(--btn-hover) 100%
  );
}

.tutorial-btn-secondary {
  background: var(--card-bg);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.tutorial-btn-secondary:hover {
  background: var(--hover-bg);
  transform: translateY(-1px);
}

/* Tutorial Overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tutorial-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Tutorial Spotlight */
.tutorial-spotlight {
  position: absolute;
  border-radius: 10px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
  transition: all 0.5s ease;
  pointer-events: none;
}

/* Tutorial Tooltip */
.tutorial-tooltip {
  position: absolute;
  background: var(--container-bg);
  border-radius: 15px;
  padding: 1.5rem;
  max-width: 350px;
  min-width: 280px;
  box-shadow: 0 15px 40px var(--shadow-color);
  transform: scale(0.9);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.tutorial-overlay.show .tutorial-tooltip {
  transform: scale(1);
}

.tutorial-tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.tutorial-step-counter {
  background: linear-gradient(
    135deg,
    var(--accent-color) 0%,
    var(--header-bg) 100%
  );
  color: var(--header-text);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tutorial-close-btn {
  background: none;
  border: none;
  color: var(--text-color);
  opacity: 0.6;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tutorial-close-btn:hover {
  background: var(--card-bg);
  color: var(--text-color);
  opacity: 1;
}

.tutorial-tooltip-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem;
}

.tutorial-tooltip-text {
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0 0 1.5rem;
}

.tutorial-tooltip-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

/* Tutorial Arrow */
.tutorial-tooltip::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.tutorial-tooltip.arrow-top::before {
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: var(--container-bg);
}

.tutorial-tooltip.arrow-bottom::before {
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: var(--container-bg);
}

.tutorial-tooltip.arrow-left::before {
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: var(--container-bg);
}

.tutorial-tooltip.arrow-right::before {
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: var(--container-bg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .tutorial-welcome-content {
    padding: 1.5rem;
    margin: 1rem;
  }

  .tutorial-welcome-features {
    grid-template-columns: 1fr;
  }

  .tutorial-welcome-footer {
    flex-direction: column;
  }

  .tutorial-tooltip {
    max-width: 280px;
    min-width: 250px;
    padding: 1rem;
  }

  .tutorial-tooltip-footer {
    flex-direction: column;
  }
}

/* Enhanced Mobile Responsiveness */

/* Extra small devices (phones, 320px and up) */
@media (max-width: 480px) {
  .tutorial-welcome-modal {
    padding: 0.5rem;
    align-items: center;
    justify-content: center;
  }

  .tutorial-welcome-content {
    padding: 1rem;
    margin: 0.5rem auto;
    border-radius: 15px;
    max-width: calc(100vw - 1rem);
    width: calc(100vw - 1rem);
    position: static;
    left: auto;
    right: auto;
    transform: none;
    box-sizing: border-box;
  }

  .tutorial-welcome-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .tutorial-welcome-title {
    font-size: 1.4rem;
    margin-bottom: 0.25rem;
    text-align: center;
  }

  .tutorial-welcome-subtitle {
    font-size: 0.9rem;
    text-align: center;
  }

  .tutorial-language-selector {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
    margin: 0.75rem 0 1rem;
  }

  .tutorial-language-label {
    font-size: 0.8rem;
    justify-content: center;
  }

  .tutorial-language-select {
    min-width: 120px;
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .tutorial-welcome-features {
    gap: 0.5rem;
    margin: 1rem 0;
  }

  .tutorial-feature {
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 8px;
  }

  .tutorial-feature i {
    font-size: 1rem;
  }

  .tutorial-welcome-description {
    font-size: 0.9rem;
    margin: 1rem 0;
  }

  .tutorial-welcome-footer {
    gap: 0.75rem;
    margin-top: 1.5rem;
  }

  .tutorial-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
    border-radius: 8px;
    min-width: 100px;
    flex: 1;
    max-width: 140px;
  }

  .tutorial-tooltip {
    max-width: calc(100vw - 2rem);
    min-width: calc(100vw - 2rem);
    padding: 1rem;
    border-radius: 10px;
    margin: 0;
    left: 1rem !important;
    right: 1rem !important;
    transform: none !important;
    position: fixed !important;
    width: calc(100vw - 2rem) !important;
  }

  .tutorial-tooltip-title {
    font-size: 1.1rem;
  }

  .tutorial-tooltip-text {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .tutorial-tooltip-footer {
    flex-direction: row;
    gap: 0.75rem;
    justify-content: space-between;
  }

  .tutorial-step-counter {
    font-size: 0.75rem;
    padding: 0.2rem 0.6rem;
  }

  /* Adjust arrow positioning for mobile */
  .tutorial-tooltip.arrow-top::before,
  .tutorial-tooltip.arrow-bottom::before {
    border-width: 8px;
  }

  .tutorial-tooltip.arrow-left::before,
  .tutorial-tooltip.arrow-right::before {
    border-width: 8px;
  }
}

/* Small devices (landscape phones, 481px to 768px) */
@media (max-width: 768px) and (min-width: 481px) {
  .tutorial-welcome-content {
    max-width: 450px;
    padding: 1.5rem;
  }

  .tutorial-welcome-features {
    grid-template-columns: 1fr 1fr;
  }

  .tutorial-tooltip {
    max-width: 320px;
    min-width: 280px;
  }

  .tutorial-btn {
    padding: 0.65rem 1.25rem;
    font-size: 0.9rem;
  }
}

/* Medium devices (tablets, 769px to 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  .tutorial-welcome-content {
    max-width: 500px;
  }

  .tutorial-tooltip {
    max-width: 350px;
  }
}

/* Large devices (desktops, 1025px and up) */
@media (min-width: 1025px) {
  .tutorial-welcome-content {
    max-width: 550px;
  }

  .tutorial-tooltip {
    max-width: 380px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .tutorial-welcome-content {
    max-height: 90vh;
    overflow-y: auto;
    padding: 1rem;
  }

  .tutorial-welcome-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .tutorial-welcome-title {
    font-size: 1.3rem;
  }

  .tutorial-welcome-features {
    margin: 0.75rem 0;
  }

  .tutorial-tooltip {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .tutorial-welcome-icon,
  .tutorial-step-counter {
    /* Ensure crisp rendering on high DPI displays */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .tutorial-welcome-modal,
  .tutorial-overlay,
  .tutorial-tooltip,
  .tutorial-welcome-content {
    transition: none;
  }

  .tutorial-welcome-icon {
    animation: none;
  }

  .tutorial-btn:hover {
    transform: none;
  }
}

/* Mobile button layout improvements */
@media (max-width: 480px) {
  .tutorial-tooltip-footer,
  .tutorial-welcome-footer {
    flex-direction: row !important;
    gap: 0.5rem;
    justify-content: space-between;
  }

  .tutorial-btn {
    flex: 1;
    min-width: 80px;
    max-width: 120px;
    font-size: 0.8rem;
    padding: 0.6rem 0.8rem;
  }

  /* Maintain button order on mobile for LTR */
  #tutorial-prev-btn {
    order: 1;
  }

  #tutorial-next-btn {
    order: 2;
  }

  #tutorial-skip-btn {
    order: 1;
  }

  #tutorial-start-btn {
    order: 2;
  }

  /* RTL button order on mobile */
  [dir="rtl"] #tutorial-next-btn {
    order: 1;
  }

  [dir="rtl"] #tutorial-prev-btn {
    order: 2;
  }

  [dir="rtl"] #tutorial-start-btn {
    order: 1;
  }

  [dir="rtl"] #tutorial-skip-btn {
    order: 2;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .tutorial-btn {
    min-height: 44px; /* iOS recommended touch target size */
    padding: 0.75rem 1rem;
  }

  .tutorial-close-btn {
    min-width: 44px;
    min-height: 44px;
    padding: 0.5rem;
  }

  /* Fix button colors on touch devices */
  .tutorial-btn-primary {
    background: linear-gradient(
      135deg,
      var(--accent-color) 0%,
      var(--header-bg) 100%
    ) !important;
    color: var(--header-text) !important;
  }

  .tutorial-btn-primary:active,
  .tutorial-btn-primary:focus {
    background: linear-gradient(
      135deg,
      var(--accent-color-hover) 0%,
      var(--btn-hover) 100%
    ) !important;
  }

  .tutorial-btn-secondary {
    background: var(--card-bg) !important;
    color: var(--text-color) !important;
    border: 2px solid var(--border-color) !important;
  }

  .tutorial-btn-secondary:active,
  .tutorial-btn-secondary:focus {
    background: var(--hover-bg) !important;
  }

  /* Remove hover effects on touch devices but maintain colors */
  .tutorial-btn:hover {
    transform: none;
  }

  .tutorial-close-btn:hover {
    transform: none;
    background: var(--card-bg);
  }
}

/* RTL Language Support */
[dir="rtl"] .tutorial-welcome-content,
[dir="rtl"] .tutorial-tooltip {
  text-align: right;
}

[dir="rtl"] .tutorial-welcome-features {
  direction: rtl;
}

[dir="rtl"] .tutorial-feature {
  flex-direction: row-reverse;
}

[dir="rtl"] .tutorial-language-selector {
  flex-direction: row-reverse;
}

[dir="rtl"] .tutorial-language-label {
  flex-direction: row-reverse;
}

/* LTR Button Order - Previous left, Next right */
.tutorial-tooltip-footer {
  flex-direction: row;
  justify-content: space-between;
}

#tutorial-prev-btn {
  order: 1;
}

#tutorial-next-btn {
  order: 2;
}

/* RTL Button Order - Next left, Previous right */
[dir="rtl"] .tutorial-tooltip-footer {
  flex-direction: row;
  justify-content: space-between;
}

[dir="rtl"] #tutorial-next-btn {
  order: 1;
}

[dir="rtl"] #tutorial-prev-btn {
  order: 2;
}

/* LTR Welcome Modal Button Order - Skip left, Start right */
.tutorial-welcome-footer {
  flex-direction: row;
  justify-content: space-between;
}

#tutorial-skip-btn {
  order: 1;
}

#tutorial-start-btn {
  order: 2;
}

/* RTL Welcome Modal Button Order - Start left, Skip right */
[dir="rtl"] .tutorial-welcome-footer {
  flex-direction: row;
  justify-content: space-between;
}

[dir="rtl"] #tutorial-start-btn {
  order: 1;
}

[dir="rtl"] #tutorial-skip-btn {
  order: 2;
}

/* RTL Step Counter Fix */
[dir="rtl"] .tutorial-step-counter {
  direction: rtl;
  unicode-bidi: embed;
}

/* RTL Button Spacing */
[dir="rtl"] .tutorial-btn {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .tutorial-btn:last-child {
  margin-right: 0;
}

/* RTL Arrow Positioning */
[dir="rtl"] .tutorial-tooltip.arrow-left::before {
  right: -20px;
  left: auto;
  border-left-color: var(--container-bg);
  border-right-color: transparent;
}

[dir="rtl"] .tutorial-tooltip.arrow-right::before {
  left: -20px;
  right: auto;
  border-right-color: var(--container-bg);
  border-left-color: transparent;
}
}
