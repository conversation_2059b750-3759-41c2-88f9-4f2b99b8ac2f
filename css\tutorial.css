/* Tutorial System Styles */

/* Tutorial Welcome Modal */
.tutorial-welcome-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tutorial-welcome-modal.show {
  opacity: 1;
  visibility: visible;
}

.tutorial-welcome-content {
  background: var(--bg-color, #ffffff);
  border-radius: 20px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
  text-align: center;
}

.tutorial-welcome-modal.show .tutorial-welcome-content {
  transform: scale(1) translateY(0);
}

.tutorial-welcome-header {
  margin-bottom: 1.5rem;
}

.tutorial-welcome-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.tutorial-welcome-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color, #333);
  margin: 0 0 0.5rem;
}

.tutorial-welcome-subtitle {
  color: var(--text-secondary, #666);
  font-size: 1rem;
  margin: 0;
}

.tutorial-welcome-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1.5rem 0;
}

.tutorial-feature {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 10px;
  font-size: 0.9rem;
  color: var(--text-color, #333);
}

.tutorial-feature i {
  color: #667eea;
  font-size: 1.1rem;
}

.tutorial-welcome-description {
  color: var(--text-secondary, #666);
  line-height: 1.6;
  margin: 1.5rem 0;
}

.tutorial-welcome-footer {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Tutorial Buttons */
.tutorial-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  text-decoration: none;
}

.tutorial-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tutorial-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.tutorial-btn-secondary {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-secondary, #666);
  border: 2px solid var(--border-color, #e9ecef);
}

.tutorial-btn-secondary:hover {
  background: var(--border-color, #e9ecef);
  transform: translateY(-1px);
}

/* Tutorial Overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tutorial-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Tutorial Spotlight */
.tutorial-spotlight {
  position: absolute;
  border-radius: 10px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
  transition: all 0.5s ease;
  pointer-events: none;
}

/* Tutorial Tooltip */
.tutorial-tooltip {
  position: absolute;
  background: var(--bg-color, #ffffff);
  border-radius: 15px;
  padding: 1.5rem;
  max-width: 350px;
  min-width: 280px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.tutorial-overlay.show .tutorial-tooltip {
  transform: scale(1);
}

.tutorial-tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.tutorial-step-counter {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.tutorial-close-btn {
  background: none;
  border: none;
  color: var(--text-secondary, #666);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tutorial-close-btn:hover {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-color, #333);
}

.tutorial-tooltip-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color, #333);
  margin: 0 0 0.5rem;
}

.tutorial-tooltip-text {
  color: var(--text-secondary, #666);
  line-height: 1.6;
  margin: 0 0 1.5rem;
}

.tutorial-tooltip-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

/* Tutorial Arrow */
.tutorial-tooltip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 10px solid transparent;
}

.tutorial-tooltip.arrow-top::before {
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: var(--bg-color, #ffffff);
}

.tutorial-tooltip.arrow-bottom::before {
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: var(--bg-color, #ffffff);
}

.tutorial-tooltip.arrow-left::before {
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: var(--bg-color, #ffffff);
}

.tutorial-tooltip.arrow-right::before {
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: var(--bg-color, #ffffff);
}

/* Responsive Design */
@media (max-width: 768px) {
  .tutorial-welcome-content {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .tutorial-welcome-features {
    grid-template-columns: 1fr;
  }
  
  .tutorial-welcome-footer {
    flex-direction: column;
  }
  
  .tutorial-tooltip {
    max-width: 280px;
    min-width: 250px;
    padding: 1rem;
  }
  
  .tutorial-tooltip-footer {
    flex-direction: column;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tutorial-welcome-content,
  .tutorial-tooltip {
    background: #2d3748;
    color: #e2e8f0;
  }
  
  .tutorial-welcome-title,
  .tutorial-tooltip-title {
    color: #f7fafc;
  }
  
  .tutorial-feature {
    background: #4a5568;
  }
  
  .tutorial-btn-secondary {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
  }
  
  .tutorial-close-btn:hover {
    background: #4a5568;
  }
}
